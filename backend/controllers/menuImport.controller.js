const Controller = require('./Controller');
const MenuImportService = require('../services/menuImport.service');
const MenuIngredientImportService = require('../services/menuIngredientImport.service');
const logger = require('../logger');

/**
 * Contrôleur pour l'import de menus depuis des fichiers Excel
 */
class MenuImportController extends Controller {

  /**
   * POST /api/menu-import/restaurant/:serviceId
   * Importer un menu restaurant depuis un fichier Excel
   */
  static async importRestaurantMenu(request, response) {
    try {
      const { serviceId } = request.params;
      const { options = {} } = request.body;

      logger.info(`Import restaurant menu requested for service ${serviceId}`);

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Vérifier le type de fichier
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!allowedMimeTypes.includes(request.file.mimetype)) {
        return response.status(400).json({
          success: false,
          message: 'Le fichier doit être un fichier Excel (.xlsx ou .xls)'
        });
      }

      // Valider le serviceId
      if (!serviceId || isNaN(parseInt(serviceId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de service invalide'
        });
      }

      // Importer le menu avec création automatique des liens ingrédients
      const result = await MenuIngredientImportService.importRestaurantMenuWithIngredients(
        parseInt(serviceId),
        request.file.buffer,
        options
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error in importRestaurantMenu:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import du menu restaurant'
      });
    }
  }

  /**
   * POST /api/menu-import/bar/:serviceId
   * Importer une carte bar depuis un fichier Excel
   */
  static async importBarMenu(request, response) {
    try {
      const { serviceId } = request.params;
      const { options = {} } = request.body;

      logger.info(`Import bar menu requested for service ${serviceId}`);

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Vérifier le type de fichier
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!allowedMimeTypes.includes(request.file.mimetype)) {
        return response.status(400).json({
          success: false,
          message: 'Le fichier doit être un fichier Excel (.xlsx ou .xls)'
        });
      }

      // Valider le serviceId
      if (!serviceId || isNaN(parseInt(serviceId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de service invalide'
        });
      }

      // Importer la carte avec création automatique des liens ingrédients
      const result = await MenuIngredientImportService.importBarMenuWithIngredients(
        parseInt(serviceId),
        request.file.buffer,
        options
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error in importBarMenu:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import de la carte bar'
      });
    }
  }

  /**
   * POST /api/menu-import/validate/restaurant
   * Valider un fichier Excel de menu restaurant sans l'importer
   */
  static async validateRestaurantMenu(request, response) {
    try {
      logger.info('Validate restaurant menu requested');

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Parser le fichier Excel
      const XLSX = require('xlsx');
      const workbook = XLSX.read(request.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      // Valider les données avec support des ingrédients
      const validationResult = MenuIngredientImportService.validateMenuWithIngredientsData(data);

      response.json({
        success: validationResult.isValid,
        data: {
          isValid: validationResult.isValid,
          errors: validationResult.errors,
          rowCount: data.length,
          preview: data.slice(0, 5) // Aperçu des 5 premières lignes
        },
        message: validationResult.isValid 
          ? 'Fichier valide, prêt pour l\'import'
          : 'Erreurs de validation détectées'
      });

    } catch (error) {
      logger.error('Error in validateRestaurantMenu:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation du fichier'
      });
    }
  }

  /**
   * POST /api/menu-import/validate/bar
   * Valider un fichier Excel de carte bar sans l'importer
   */
  static async validateBarMenu(request, response) {
    try {
      logger.info('Validate bar menu requested');

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Parser le fichier Excel
      const XLSX = require('xlsx');
      const workbook = XLSX.read(request.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      // Valider les données avec support des ingrédients
      const validationResult = MenuIngredientImportService.validateBarMenuWithIngredientsData(data);

      response.json({
        success: validationResult.isValid,
        data: {
          isValid: validationResult.isValid,
          errors: validationResult.errors,
          rowCount: data.length,
          preview: data.slice(0, 5) // Aperçu des 5 premières lignes
        },
        message: validationResult.isValid 
          ? 'Fichier valide, prêt pour l\'import'
          : 'Erreurs de validation détectées'
      });

    } catch (error) {
      logger.error('Error in validateBarMenu:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation du fichier'
      });
    }
  }

  /**
   * GET /api/menu-import/status/:serviceId
   * Obtenir le statut d'import pour un service
   */
  static async getImportStatus(request, response) {
    try {
      const { serviceId } = request.params;

      logger.info(`Import status requested for service ${serviceId}`);

      // Valider le serviceId
      if (!serviceId || isNaN(parseInt(serviceId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de service invalide'
        });
      }

      // Récupérer les statistiques du service
      const db = require('../db');
      const client = await db.getClient();

      try {
        // Compter les produits par catégorie
        const productsResult = await client.query(`
          SELECT 
            cp.nom as categorie,
            COUNT(p.produit_id) as count
          FROM "CategoriesProduits" cp
          LEFT JOIN "Produits" p ON cp.categorie_id = p.categorie_id
          WHERE cp.service_id = $1
          GROUP BY cp.categorie_id, cp.nom
          ORDER BY cp.ordre_affichage
        `, [parseInt(serviceId)]);

        // Compter le total de produits
        const totalResult = await client.query(`
          SELECT COUNT(*) as total
          FROM "Produits" p
          JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
          WHERE cp.service_id = $1
        `, [parseInt(serviceId)]);

        const status = {
          serviceId: parseInt(serviceId),
          totalProducts: parseInt(totalResult.rows[0].total),
          categoriesCount: productsResult.rows.length,
          productsByCategory: productsResult.rows,
          hasMenu: parseInt(totalResult.rows[0].total) > 0,
          lastImport: null // TODO: Ajouter une table pour tracker les imports
        };

        response.json({
          success: true,
          data: status,
          message: 'Statut d\'import récupéré avec succès'
        });

      } finally {
        client.release();
      }

    } catch (error) {
      logger.error('Error in getImportStatus:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du statut'
      });
    }
  }
}

module.exports = MenuImportController;
